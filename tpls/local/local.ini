[contact]
trustedAssignEmail = '<EMAIL>'
alertSMS = '01234556'
alertEmail = '<EMAIL>'
defaultReciveEmail =''
projectFubEmail = '<EMAIL>'

[importNotify]
bcreTo = ["<EMAIL>"]
defaultTo = ["<EMAIL>"]

[dbs.chome]
uri = "mongodb://d1:d1@************:27017,************:27018/chomeca?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=1&tls=false"

[dbs.rni]
uri = "***************************************************************************************************************************************************************"

[dbs.tmp]
uri = "***************************************************************************************************************************************************************"

[dbs.vow]
uri = "mongodb://d1:d1@************:27017,************:27018/vow?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=1&tls=false"

[log]
path = "./logs"

[server]
host = "0.0.0.0"

[serverBase]
srcPath = "../appweb/src"
srcGoAppPath = "../go/goapp/src"
srcGoBasePath = "../go"

[session.redis]
host = "************"

[elastic]
cert = "/mnt/mac/opt/homebrew/Cellar/elasticsearch-full/7.17.4/libexec/elastic-stack-ca.p12"
host = "https://************:9200"

[pyfpimg.imageSource]
local_path_cda = "/home/<USER>"

[pyfpimg.kafka.request]
bootstrap_servers = "************:9092"

[pyfpimg.kafka.response]
_test_topic_name = "test-fpimg-response"
bootstrap_servers = "************:9092"

[pyfpimg.mongo]
host = "************"

[azure]
subscriptionKey = "e678a9f6c8764711849e0f17504aa696"

[golog]
dir = "/home/<USER>/github/go/goapp/logs"
#level = "info"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "txt"
standard_output = true