[contact]
trustedAssignEmail = '<EMAIL>'
alertSMS = '01234556'
alertEmail = '<EMAIL>'
defaultReciveEmail =''
projectFubEmail = '<EMAIL>'

[importNotify]
bcreTo = ["<EMAIL>"]
defaultTo = ["<EMAIL>"]

[dbs.chome]
uri="mongodb://d1:d1@ca12:27017,ca12a:27018/data?authSource=admin&replicaSet=dev&tls=true&tlsCAFile=/etc/mongo/ca.crt&tlsCertificateKeyFile=/etc/mongo/client/ca12.pem&tlsAllowInvalidCertificates=true&readPreference=nearest&readPreferenceTags=dc:canada,nm:d1&minPoolSize=1&maxPoolSize=1"

[dbs.rni]
uri="**********************************************************************************************************************************************************************************************************************************************************************************************"

[dbs.tmp]
uri="**********************************************************************************************************************************************************************************************************************************************************************************************"

[dbs.vow]
uri="mongodb://d1:d1@ca12:27017,ca12a:27018/listing?authSource=admin&replicaSet=dev&tls=true&tlsCAFile=/etc/mongo/ca.crt&tlsCertificateKeyFile=/etc/mongo/client/ca12.pem&tlsAllowInvalidCertificates=true&readPreference=nearest&readPreferenceTags=dc:canada,nm:d1&minPoolSize=1&maxPoolSize=1"

[elastic]
cert = "/etc/elasticsearch/certs/http_ca.crt"
host = "https://localhost:9200"
password = "E=0*4p9URuWf-jqZpg1b"
user = "elastic"

[mailEngineList.0]
accessKeyId = "********************"
email = "<EMAIL>"
engine = "SES"
region = "us-east-1"
secretAccessKey = "Uogp6eMqmbIhxB43mqCYPJYBT9x3vnQZwrawvz6W"
url = "http://934.230.229.12:8088/ses.php1"

[user_file]
disableUpload = false
folder = "G"
port = 8_091
protocol = "https"
wwwbase = "f.realmaster.com"

[kafka.main]
ignoreThisServer = true

[share]
host = "https://d1w.realmaster.com"
hostNameCn = "d1w.realmaster.com"
secret = "RMT547dc488a8d7d5828d34437872064"

[wechat]
appId = "wx486c08aa3a05089d"
appName = "RealMaster"
domain = "d1w.realmaster.com"
secret = "2b37e59a16594a8faebaa509a0da9956"

[wechatWeb]
AppID = "wxf43abc63fd6c1b19"
AppSecret = "fcd6d3800bb2906bacbee676685a0de4"

[log]
path = "./logs"

[server]
port = 8_081

[serverBase]
wwwDomain = 'd1w.realmaster.com'
appHostUrl = "https://d1.realmaster.com"
canonicalHost = 'http://d1w.realmaster.com'
srcPath = "../appweb/src"
srcGoAppPath = "../go/goapp/src"
srcGoBasePath = "../go"

[settings.www.match]
hostname = "d1w.realmaster.(com|cn)"

[settings.appWeb.match]
hostname = "d1.realmaster.(com|cn)"

[settings.shareWeb.match]
hostname = "https://d1w.realmaster.com"

[pyfpimg.mongo]
collection = "floorplan_classification"
host = "ca12"
name = "listing"
password = "d1"
port = 27_017
tlsCAFile = "/etc/mongo/ca.crt"
user = "d1"

[fileBase]
imgServerDlAddr = "http://f.realmaster.cn"

[azure]
subscriptionKey = "e678a9f6c8764711849e0f17504aa696"

[golog]
dir = "/home/<USER>/github/go/goapp/logs"
#level = "info"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "txt"
standard_output = true