#!/bin/bash
#https://unix.stackexchange.com/questions/31414/how-can-i-pass-a-command-line-argument-into-a-shell-script

# exit on error
set -e

usage()
{
  echo ""
  echo "Usage: $0 -m module -r report -f filepath -c filecsv -v modulecsv -b -e"
  echo -e "\t-m test module name, default all test module"
  echo -e "\t-r generate report"
  echo -e "\t-b run batch, no webtest, do not start server "
  echo -e "\t-f test single file"
  echo -e "\t-c test files from csv"
  echo -e "\t-v test modules from csv"
  echo -e "\t-e isElasitcSearch"
  exit 1 # Exit script after printing help
}

# echo "args \t= $@"
printf "args \t= %s\n" "$*"


while getopts ":m::f::c::v:rbe" o; do
    # echo "o \t= $o"
    case "${o}" in
        m)
            module=${OPTARG}
            ;;
        r)
            report=Y
            ;;
        b)
            batch=Y
            ;;
        f)
            file=${OPTARG}
            ;;
        c)
            fcsv=${OPTARG}
            ;;
        v)  
            mcsv=${OPTARG}
            ;;
        e)
            isElastic=Y
            ;;
        *)
            usage
            ;;
    esac
done


# Begin script in case all parameters are correct
echo -e "batch \t= $batch"
echo -e "module \t= $module"
echo -e "report \t= $report"
echo -e "file \t= $file"
echo -e "file from csv \t= $fcsv"
echo -e "isElastic \t= $isElastic"
echo -e "module from csv \t= $mcsv"

BUILT_DIR=built
UNIT_TEST_DIR=unitTest

# TOOD:
# In unitTest/test.sh line 69:
# CURBASEDIR=$(dirname $(dirname $0))
#                      ^-----------^ SC2046 (warning): Quote this to prevent word splitting.
#                                ^-- SC2086 (info): Double quote to prevent globbing and word splitting.
# Set CURBASEDIR to the parent directory of the script's current path
CURBASEDIR=$(dirname $(dirname $0))
cd $CURBASEDIR


echo -e "Start test.sh ts \t= `date +%F\ %T`"
HOME=${HOME-`pwd`}
echo -e "HOME \t= $HOME"
. $HOME/.nvm/nvm.sh


CFG_DIR=$PWD


# Check if CFG_PATH is not set or is empty
if [ -z "$CFG_PATH" ]; then
  # Check if $CFG_DIR/built/unit_test.ini exists
  if [ -f "$CFG_DIR/built/config/unit_test.ini" ]; then
    # Set CFG_PATH to the file
    CFG_PATH="$CFG_DIR/built/config/unit_test.ini"
    echo -e "CFG_PATH \t= $CFG_PATH"
  else
    # Report error and exit if the file doesn't exist
    echo "Error: not set CFG_PATH and $CFG_DIR/built/config/unit_test.ini not found."
    exit 1
  fi
else
  echo "CFG_PATH is already set to $CFG_PATH"
fi



SRC_DIR=$(coffee $CFG_DIR/bin/getValueFromToml.coffee "$CFG_PATH" "serverBase.srcPath")
if [ -z "$SRC_DIR" ]; then # If SRC_DIR is empty, set it to the default value
  SRC_DIR=$(realpath "$CFG_DIR/../appweb/src")
fi

coffee $CFG_DIR/config.coffee -s $SRC_DIR
# coffee $CFG_DIR/setup.coffee
echo -e "CFG_DIR \t= $CFG_DIR"

# TODO: should recompile?
# echo $SRC_DIR/lib/coffeemate4.coffee $CFG_PATH compile
# $NODE_PATH $COFFEE_PATH $SRC_DIR/lib/coffeemate4.coffee $CFG_PATH compile


# export RMBASE_FILE_PHP=$CFG_DIR/$BUILT_DIR/base.php
# export RMBASE_FILE_NODEJS=$CFG_DIR/$BUILT_DIR/base.coffee
# export RMBASE_FILE_PYTHON=$CFG_DIR/$BUILT_DIR/base.py
# export RMBASE_FILE_CFG4=$CFG_DIR/$BUILT_DIR/config4.coffee
# export RMBASE_FILE_CFG=$RMBASE_FILE_CFG4
# export RMBASE_FILE_BASE_YML=$CFG_DIR/base.yml

if [ "$isElastic" = 'Y' ]; then
  echo -e "isElastic\t=$isElastic, use unit_testES.ini"
  export RMBASE_FILE_CFG=$CFG_DIR/$BUILT_DIR/rmIni/unit_testES.ini
else
  echo -e "isElastic\t=$isElastic, use unit_test.ini"
  export RMBASE_FILE_CFG=$CFG_DIR/$BUILT_DIR/rmIni/unit_test.ini
fi
export VHOST_FILE_CFG=$CFG_DIR/$BUILT_DIR/config/vhost.ini
export CFG_DIR=$CFG_DIR
export SRC_DIR=$SRC_DIR
export BUILT_DIR=$BUILT_DIR
export report=$report
export module=$module
export file=$file
export NO_WEB_TEST=$batch
export fcsv=$fcsv
export mcsv=$mcsv
str=''


str="$CFG_DIR/$UNIT_TEST_DIR/mochaTest.sh"

# cd $BASE_DIR
pwd

if [[ "$fcsv" ]]; then
  echo testing files from csv: $fcsv
  fileArray=""
  {
    read
    while IFS=',' read -ra array || [ -n "$array" ]; do
      # echo 0 "${array[0]}" #sendMail
      # echo 1 "${array[1]}" #-b
      # echo 2 "${array[2]}" #lib/sendmail.js
      searchEnv=${array[3]}
      # echo "searchEnv File \t= $searchEnv"
      if [[ $searchEnv == "Mongo" ]]
      then
        if [[ $isElastic == "Y" ]]
        then
          echo "skip test file: ${array[2]} for non-Mongo search"
        else
          fileArray+=" \"$SRC_DIR/unitTestJs/${array[2]}\" "
        fi
      elif [[ $searchEnv == "ES" ]]
      then
        if [[ $isElastic == "Y" ]]
        then
          fileArray+=" \"$SRC_DIR/unitTestJs/${array[2]}\" "
        else
          echo "skip test file: ${array[2]} for non-elastic search"
        fi
      else
        echo "skip test file: ${array[2]} for unknown search engine"
      fi
    done 
  } < $CFG_DIR/$fcsv

  echo -e "fileArray \t= $fileArray"
  if [[ $fileArray == "" ]]; then
    echo "Test Files: no file satisfy condition to test"
    exit 0
  fi
  # npx nyc --reporter=html mocha --require $SRC_DIR/unitTestJs/00_common/startServer.js --recursive "$fileArray"
  export fileArray=$fileArray
elif [[ "$mcsv" ]]; then
  echo testing modules from csv: $mcsv
  fileArray=""
  {
    read
    while IFS=',' read -ra array || [ -n "$array" ]; do
      # echo 3 "${array[3]}"
      searchEnv=${array[3]}
      # echo "searchEnv Module \t= $searchEnv"
      if [[ $searchEnv == "Mongo" ]]
      then
        if [[ $isElastic == "Y" ]]
        then
          echo "skip test file: ${array[2]} for non-Mongo search"
        else
          fileArray+=" \"$SRC_DIR/unitTestJs/${array[2]}\" "
        fi
      elif [[ $searchEnv == "ES" ]]
      then
        if [[ $isElastic == "Y" ]]
        then
          fileArray+=" \"$SRC_DIR/unitTestJs/${array[2]}\" "
        else
          echo "skip test module: ${array[2]} for non-elastic search"
        fi
      else
        echo "skip test module: ${array[2]} for unknown search engine"
      fi
    done 
  } < $CFG_DIR/$mcsv

  echo -e "fileArray \t= $fileArray"
  if [[ $fileArray == "" ]]; then
    echo "Test Modules: no file satisfy condition to test"
    exit 0
  fi
  # npx nyc --reporter=html mocha --require $SRC_DIR/unitTestJs/00_common/startServer.js --recursive "$fileArray"
  export fileArray=$fileArray
else
  export fileArray=""
fi

export isElastic=$isElastic

echo "run test: $str"
eval $str