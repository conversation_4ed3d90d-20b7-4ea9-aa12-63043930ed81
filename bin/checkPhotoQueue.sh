#!/bin/bash

# MongoDB Photo Queue Monitoring Script
#
# Features:
# - Monitor high priority tasks in reso_photo_download_queue collection
# - Automatically read email addresses and database connections from local.ini
# - Save historical data (last 10 checks) and display trends in emails
# - Send queue status information periodically
#
# Usage:
# 1. Manual run:
#    ./bin/checkPhotoQueue.sh                              # Use settings from config file
#    ./bin/checkPhotoQueue.sh "<EMAIL>"     # Specify recipient
#    ./bin/checkPhotoQueue.sh "<EMAIL>" 40000  # Specify recipient and threshold
#    SIMPLE_MODE=true ./bin/checkPhotoQueue.sh             # Send simplified email
#    DEBUG=true ./bin/checkPhotoQueue.sh                   # Display complete email content
#
# 2. Set up scheduled task (run every 3 hours):
#    crontab -e
#    # Add the following line:
#    0 */3 * * * /path/to/rmconfig/bin/checkPhotoQueue.sh >> /tmp/photo_queue_monitor.log 2>&1
#
# 3. View logs and historical data:
#    tail -f /tmp/photo_queue_monitor.log                  # View run logs
#    cat logs/photo_queue_history.json | jq .             # View historical data
#
# Configuration:
# - <PERSON><PERSON><PERSON> automatically reads email address from local.ini: contact.alertEmail
# - Script automatically reads MongoDB connection from local.ini: dbs.rni.uri
#
# Based on query: db.reso_photo_download_queue.find({priority:{$gt:35000}}).count()

# Email configuration
RM_FROM="<EMAIL>"
RM_URL="https://ml1.realmaster.cc/send"

# Historical data file
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
HISTORY_FILE="$SCRIPT_DIR/../logs/photo_queue_history.json"
LOCAL_CONFIG_FILE="$SCRIPT_DIR/../local.ini"

# Ensure log directory exists
mkdir -p "$(dirname "$HISTORY_FILE")"

# Parameter processing
RM_TO="$1"
THRESHOLD="$2"

# Read default values from config file
echo "Reading settings from config file..."

# Read email address
if [ -z "$RM_TO" ]; then
    if [ -f "$LOCAL_CONFIG_FILE" ]; then
        CONFIG_EMAIL=$(coffee "$SCRIPT_DIR/getValueFromToml.coffee" "$LOCAL_CONFIG_FILE" "contact.alertEmail" 2>/dev/null)
        if [ -n "$CONFIG_EMAIL" ]; then
            RM_TO="$CONFIG_EMAIL"
            echo "Email address read from config file: $RM_TO"
        else
            RM_TO="<EMAIL>,<EMAIL>,<EMAIL>"
            echo "Email address not found in config file, using default: $RM_TO"
        fi
    else
        RM_TO="<EMAIL>,<EMAIL>,<EMAIL>"
        echo "Config file does not exist, using default email address: $RM_TO"
    fi
else
    echo "Using email address specified in command line: $RM_TO"
fi

# Set priority threshold
if [ -z "$THRESHOLD" ]; then
    THRESHOLD=35000
fi

# Read MongoDB connection URI from config file
if [ -f "$LOCAL_CONFIG_FILE" ]; then
    CONFIG_MONGO_URI=$(coffee "$SCRIPT_DIR/getValueFromToml.coffee" "$LOCAL_CONFIG_FILE" "dbs.rni.uri" 2>/dev/null)
    if [ -n "$CONFIG_MONGO_URI" ]; then
        MONGO_URI="$CONFIG_MONGO_URI"
        echo "MongoDB connection read from config file"
    else
        echo "ERROR: MongoDB connection (dbs.rni.uri) not found in config file: $LOCAL_CONFIG_FILE"
        echo "Please add the MongoDB connection URI to the configuration file."
        exit 1
    fi
else
    echo "ERROR: Config file does not exist: $LOCAL_CONFIG_FILE"
    echo "Please ensure the configuration file exists and contains the MongoDB connection URI (dbs.rni.uri)."
    exit 1
fi

echo "==================== Check Photo Download Queue ===================="
echo "Priority threshold: $THRESHOLD"
echo "Recipients: $RM_TO"
echo "Historical data file: $HISTORY_FILE"

# Execute MongoDB query
echo "Querying database..."

# Query high priority task count
HIGH_PRIORITY_COUNT=$(mongosh "$MONGO_URI" --quiet --eval "
db.reso_photo_download_queue.find({priority:{\$gt:$THRESHOLD}}).count()
")

# Query total task count
TOTAL_COUNT=$(mongosh "$MONGO_URI" --quiet --eval "
db.reso_photo_download_queue.find().count()
")

# Check query result
if [ $? -ne 0 ]; then
    echo "MongoDB query failed!"
    exit 1
fi

echo "Query results:"
echo "High priority tasks (priority > $THRESHOLD): $HIGH_PRIORITY_COUNT"
echo "Total task count: $TOTAL_COUNT"

# Save current data to historical records
CURRENT_TIME=$(date '+%Y-%m-%d %H:%M:%S')
CURRENT_TIMESTAMP=$(date +%s)

# Create current record
CURRENT_RECORD=$(jq -n \
    --arg timestamp "$CURRENT_TIMESTAMP" \
    --arg datetime "$CURRENT_TIME" \
    --arg high_priority "$HIGH_PRIORITY_COUNT" \
    --arg total "$TOTAL_COUNT" \
    --arg threshold "$THRESHOLD" \
    '{
        timestamp: ($timestamp | tonumber),
        datetime: $datetime,
        high_priority: ($high_priority | tonumber),
        total: ($total | tonumber),
        threshold: ($threshold | tonumber)
    }')

# Read historical data, create empty array if file doesn't exist
if [ -f "$HISTORY_FILE" ]; then
    HISTORY_DATA=$(cat "$HISTORY_FILE")
else
    HISTORY_DATA="[]"
fi

# Add current record and keep last 10 records
NEW_HISTORY=$(echo "$HISTORY_DATA" | jq --argjson current "$CURRENT_RECORD" '
    . + [$current] | sort_by(.timestamp) | .[-10:]
')

# Save updated historical data
echo "$NEW_HISTORY" > "$HISTORY_FILE"

echo "Historical data updated"

# Generate historical data table
HISTORY_TABLE=""
HISTORY_COUNT=$(echo "$NEW_HISTORY" | jq 'length')

if [ "$HISTORY_COUNT" -gt 0 ]; then
    echo "Generating historical data table..."

    # Generate table header
    HISTORY_TABLE="<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; font-family: monospace;'>
    <thead style='background-color: #f0f0f0;'>
        <tr>
            <th>Time</th>
            <th>High Priority Tasks</th>
            <th>Total Tasks</th>
            <th>High Priority %</th>
            <th>Change Trend</th>
        </tr>
    </thead>
    <tbody>"

    # Generate table rows
    for i in $(seq 0 $((HISTORY_COUNT - 1))); do
        RECORD=$(echo "$NEW_HISTORY" | jq -r ".[$i]")
        DATETIME=$(echo "$RECORD" | jq -r '.datetime')
        HIGH_PRIORITY=$(echo "$RECORD" | jq -r '.high_priority')
        TOTAL=$(echo "$RECORD" | jq -r '.total')

        # Calculate percentage (without relying on bc command)
        if [ "$TOTAL" -gt 0 ]; then
            PERCENTAGE=$(awk "BEGIN {printf \"%.2f\", $HIGH_PRIORITY * 100 / $TOTAL}")
        else
            PERCENTAGE="0.00"
        fi

        # Calculate change trend
        TREND=""
        if [ $i -gt 0 ]; then
            PREV_RECORD=$(echo "$NEW_HISTORY" | jq -r ".[$((i-1))]")
            PREV_HIGH_PRIORITY=$(echo "$PREV_RECORD" | jq -r '.high_priority')
            PREV_TOTAL=$(echo "$PREV_RECORD" | jq -r '.total')

            HIGH_DIFF=$((HIGH_PRIORITY - PREV_HIGH_PRIORITY))
            TOTAL_DIFF=$((TOTAL - PREV_TOTAL))

            if [ $HIGH_DIFF -gt 0 ]; then
                TREND="+$HIGH_DIFF"
            elif [ $HIGH_DIFF -lt 0 ]; then
                TREND="$HIGH_DIFF"
            else
                TREND="0"
            fi

            if [ $TOTAL_DIFF -ne 0 ]; then
                TREND="$TREND (Total:$TOTAL_DIFF)"
            fi
        else
            TREND="--"
        fi

        # Set style based on whether it's the latest record
        if [ $i -eq $((HISTORY_COUNT - 1)) ]; then
            ROW_STYLE="style='background-color: #ffffcc; font-weight: bold;'"
        else
            ROW_STYLE=""
        fi

        HISTORY_TABLE="$HISTORY_TABLE
        <tr $ROW_STYLE>
            <td>$DATETIME</td>
            <td>$HIGH_PRIORITY</td>
            <td>$TOTAL</td>
            <td>$PERCENTAGE%</td>
            <td>$TREND</td>
        </tr>"
    done

    HISTORY_TABLE="$HISTORY_TABLE
    </tbody>
    </table>"
fi

# Calculate statistics
STATS=""
if [ "$HISTORY_COUNT" -gt 1 ]; then
    AVG_HIGH_PRIORITY=$(echo "$NEW_HISTORY" | jq '[.[].high_priority] | add / length | floor')
    MAX_HIGH_PRIORITY=$(echo "$NEW_HISTORY" | jq '[.[].high_priority] | max')
    MIN_HIGH_PRIORITY=$(echo "$NEW_HISTORY" | jq '[.[].high_priority] | min')

    AVG_TOTAL=$(echo "$NEW_HISTORY" | jq '[.[].total] | add / length | floor')
    MAX_TOTAL=$(echo "$NEW_HISTORY" | jq '[.[].total] | max')
    MIN_TOTAL=$(echo "$NEW_HISTORY" | jq '[.[].total] | min')

    STATS="<h4>Statistics (Last ${HISTORY_COUNT} checks)</h4>
    <ul>
        <li><strong>High Priority Tasks:</strong> Average $AVG_HIGH_PRIORITY, Max $MAX_HIGH_PRIORITY, Min $MIN_HIGH_PRIORITY</li>
        <li><strong>Total Tasks:</strong> Average $AVG_TOTAL, Max $MAX_TOTAL, Min $MIN_TOTAL</li>
    </ul>"
fi

# Generate email content
EMAIL_SUBJECT="Photo Download Queue Status Report - $(date '+%m/%d %H:%M')"

# Simplified email content
SIMPLE_MODE=${SIMPLE_MODE:-false}

if [ "$SIMPLE_MODE" = true ]; then
    rmText="<h2>Photo Download Queue Status Report</h2>
<p>Check time: $CURRENT_TIME</p>
<p>High priority tasks (priority > $THRESHOLD): $HIGH_PRIORITY_COUNT</p>
<p>Total task count: $TOTAL_COUNT</p>
<p>This report is generated by automatic monitoring script</p>"
else
    rmText="<h2>Photo Download Queue Status Report</h2>
<p><strong>Check time:</strong> $CURRENT_TIME</p>
<hr>
<h3>Current Queue Status:</h3>
<ul>
<li><strong>High priority tasks (priority > $THRESHOLD):</strong> $HIGH_PRIORITY_COUNT</li>
<li><strong>Total task count:</strong> $TOTAL_COUNT</li>
</ul>
$STATS
<hr>
<h3>Historical Trends (Last ${HISTORY_COUNT} checks)</h3>
$HISTORY_TABLE
<p><em>This report is generated by automatic monitoring script - checked every 3 hours</em></p>"
fi

# Send email
if [ ${#rmText} -gt 0 ] && [ ${#RM_TO} -gt 0 ]; then
  echo "======================= Send Email ======================="
  
  mail=$(jq -n \
    --arg from "$RM_FROM" \
    --arg to "$RM_TO" \
    --arg subject "$EMAIL_SUBJECT" \
    --arg html "$rmText" \
    '{from: $from, to: $to, subject: $subject, html: $html}')
  
  # Convert recipients to array
  newEmail=$(jq -r '.to |= split(",")' <<< "${mail}")
  
  echo "Sending email: $EMAIL_SUBJECT"
  echo "Recipients: $RM_TO"

  # Display email data to be sent (for debugging)
  echo "Email data preview:"
  echo "$newEmail" | jq -r '.to, .subject' | head -2

  # If DEBUG mode is set, display complete email content
  if [ "$DEBUG" = true ]; then
    echo "Complete email content:"
    echo "$newEmail" | jq .
  fi

  # Send email
  response=$(curl -X POST \
    --header "Content-Type: application/json" \
    --header "X-RM-Auth: RM" \
    -s \
    --data "$newEmail" \
    $RM_URL)
  curl_exit=$?

  echo "Email service response: $response"

  if [ $curl_exit -eq 0 ]; then
    echo "Email sent successfully!"
    echo "Status report sent"
  else
    echo "Email sending failed!"
    exit 1
  fi
fi

echo "======================= Check Complete ======================="
exit 0
