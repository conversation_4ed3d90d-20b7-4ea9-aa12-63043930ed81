#!/bin/bash

###
# This script is used to start the appweb server, batch service, unit test service, or pyfpimg service.
# It takes the following arguments:
# -c: Specify the config file(s) to use
# -t: Specify the type of program to run (batch|pyfpimg|unit_test|unit_testES|pyml)
# -n: Specify the name of the batch (only for batch type)
# -d: Specify the directory to run the command in (only go batch [not use default srcGoAppPath]need this, mostly is package name, the ultimate path is $srcGoBasePath/$d)
#     If not set, it will use the default srcGoAppPath from local.ini or local.test.ini
# -e: Specify the email address for alerts
# -w: Specify the wanted_by value for the systemd service (default.target|multi-user.target. default: default.target.)
# -cmd: Specify the command to run (like for batch type)
# -cron: run service timer if you set this. Specify the cron timer for the batch service (y-m-d 00:00:00,like *-*-* 09:40:00 )
# -rs: restart_service, default is no, if set, the service will be restarted when it fails
# -delete: delete a specific systemd service, need to input -t and -n
# -del_failed: delete all failed systemd service
# Example: 
# NOTE: do not use sh
# 1 it is outdated https://stackoverflow.com/questions/5725296/difference-between-sh-and-bash
# 2 it is not secure https://unix.stackexchange.com/questions/786931/is-it-potentially-dangerous-to-run-a-bash-script-with-sh
# ./start.sh [-w 'multi-user.target']
# ./start.sh -t batch -n fix_floorplanId -cmd "lib/batchBase.coffee batch/condos/fix_floorplanId.coffee dryrun" [-w 'multi-user.target'] [-cron '*-*-* 09:40:00']
# ./start.sh -t unit_test [-cron '*-*-* 09:40:00']
# ./start.sh -t unit_testES [-cron '*-*-* 09:40:00']
# ./start.sh -t pyfpimg -cmd "--test"
# ./start.sh -t pyml
###

# exit on error
set -e

BUILT_DIR=built
CURBASEDIR=$(dirname $0)
cd $CURBASEDIR

date +%F\ %T
HOME=${HOME-`pwd`}
LOG_DIR='./logs'
CFG_DIR=$PWD
GROUP=$(id -gn)
USER=$(whoami)

# Initialize arrays for config files and other arguments
config_files=()
other_args=""
prog_type=""
batch_name=""
alert_email=""
wanted_by=""
cron_timer="" # y-m-d 00:00:00,like *-*-* 09:40:00 
restart_service=false
delete_service=false
delete_failed_services=false
src_go_dir=""


# Parse command-line arguments
# For other_args, use -cmd "command" to specify the command to run
while [[ $# -gt 0 ]]; do
    case $1 in
        -c)
            config_files+=("$2")
            shift 2
            ;;
        -t)
            prog_type="$2"
            shift 2
            ;;
        -n)
            batch_name="$2"
            shift 2
            ;;
        -e)
            alert_email="$2"
            shift 2
            ;;
        -w)
            wanted_by="$2"
            shift 2
            ;;
        -d)
            src_go_dir="$2"
            shift 2
            ;;
        -cmd)
            other_args="$2"
            shift 2
            ;;
        -cron)
            cron_timer="$2"
            shift 2
            ;;
        -rs)
            restart_service=true
            shift 1
            ;;
        -delete)
            delete_service=true
            shift 1
            ;;
        -del_failed)
            delete_failed_services=true
            shift 1
            ;;
        *)
            echo "unknow parameter : $1"
            shift
            ;;
    esac
done

echo $other_args


# Check the local file and set as LOCAL_CONFIG_FILE
if ([ -z "$prog_type" ]) || [[ "$prog_type" == 'batch' || "$prog_type" == 'pyfpimg' || "$prog_type" == 'pyml' ]]; then
  if [ -f "local.ini" ]; then
    LOCAL_CONFIG_FILE="local.ini"
  else
    echo "You haven't set a local environment file: local.ini"
    exit 1
  fi
elif [[ "$prog_type" == 'unit_test' || "$prog_type" == 'unit_test_timer' ]]; then
  if [ -f "local.test.ini" ]; then
    LOCAL_CONFIG_FILE="local.test.ini"
  else
    echo "You haven't set a local environment file: local.test.ini"
    exit 1
  fi
elif [[ "$prog_type" == 'unit_testES' || "$prog_type" == 'unit_testES_timer' ]]; then
  if [ -f "local.testES.ini" ]; then
    LOCAL_CONFIG_FILE="local.testES.ini"
  else
    echo "You haven't set a local environment file: local.testES.ini"
    exit 1
  fi
else
  echo "Invalid program type or missing configuration. prog_type=$prog_type"
  exit 1
fi
echo "Local config file: $LOCAL_CONFIG_FILE"


SRC_DIR=$(coffee bin/getValueFromToml.coffee "$LOCAL_CONFIG_FILE" "serverBase.srcPath")
SRC_DIR=$(realpath $SRC_DIR)
SRC_GOAPP_DIR=$(coffee bin/getValueFromToml.coffee "$LOCAL_CONFIG_FILE" "serverBase.srcGoAppPath")
SRC_GO_BASE_DIR=$(coffee bin/getValueFromToml.coffee "$LOCAL_CONFIG_FILE" "serverBase.srcGoBasePath")
SRC_GO_BASE_DIR=$(realpath "$SRC_GO_BASE_DIR")
if [ -z "$SRC_DIR" ]; then # If SRC_DIR is empty, set it to the default value
  SRC_DIR=$(realpath "$CFG_DIR/../appweb/src")
fi
echo "SRC path is: $SRC_DIR"
PYFPIMG_DIR=$(realpath "$CFG_DIR/../pyfpimg")
CMATE3_PATH=$(realpath "$SRC_DIR/lib/cmate3.coffee")
CONFIG_PATH="$CFG_DIR/$BUILT_DIR/run/config.ini"
BUILT_CONFIG_DIR="$CFG_DIR/$BUILT_DIR/config"
LOCAL_RMINI_DIR="$CFG_DIR/$BUILT_DIR/rmIni"
ETC_RMINI_DIR="$CFG_DIR/$BUILT_DIR/etc/rmIni"
NODE_PATH=$(which node)
COFFEE_PATH=$(which coffee)

SYSTEMD_PATH=$HOME/.config/systemd/user/
mkdir -p $SYSTEMD_PATH
mkdir -p $LOCAL_RMINI_DIR
mkdir -p $ETC_RMINI_DIR
mkdir -p $BUILT_CONFIG_DIR
# if log.path is set in the local config file, use it as the log directory
LOG_PATH=$(coffee bin/getValueFromToml.coffee "$LOCAL_CONFIG_FILE" "log.path")
# Convert the log path to an absolute path if it exists
if [ -n "$LOG_PATH" ]; then
  LOG_DIR=$(realpath "$LOG_PATH")
  mkdir -p $LOG_DIR
fi



# if user doesn't input alert_email, use localAlertEmail in LOCAL_CONFIG_FILE as the default alert_email
localAlertEmail=$(coffee bin/getValueFromToml.coffee "$LOCAL_CONFIG_FILE" "contact.alertEmail")
if [ -z "$alert_email" ]; then
  if [ -n "$localAlertEmail" ]; then
      alert_email="$localAlertEmail"
      echo "alert_email is set to $alert_email"
  else
      alert_email="<EMAIL>,<EMAIL>"
      echo "alert_email is not set in $LOCAL_CONFIG_FILE, use <NAME_EMAIL>, <EMAIL>"
  fi
else
  echo "alert_email is set to $alert_email"
fi


# run build config
# NOTE: TODO: remove this step, it's not needed when testEs, and causes error when local.ini is not set
#if [ "$LOCAL_CONFIG_FILE" == "local.ini" ]; then
coffee config.coffee -e $alert_email -s $SRC_DIR
#else
#  echo "skip running config.coffee, because LOCAL_CONFIG_FILE is not local.ini"
#fi


# 0. Handle multiple config files if specified
# If multiple config files are provided, merge them and save in built/run/config.ini
# Example: ./start.sh -c geocode.ini -c sqft.ini
merged_config="${BUILT_DIR}/config/config_tmp.ini"
# If config files are specified, merge them using CoffeeScript
if [ ${#config_files[@]} -gt 0 ]; then
    # Generate a unique merged config file
    coffee bin/mergeConfigs.coffee "$merged_config" "${config_files[@]}" 
else
    # Use default config file
    coffee bin/mergeConfigs.coffee "$merged_config" "$BUILT_CONFIG_DIR/app_web.ini"
fi
export MERGED_CFG="$CFG_DIR/$merged_config"
# Copy the config file to `built/rmIni/app_web.ini`
APPWEB_CONFIG_PATH="$LOCAL_RMINI_DIR/app_web.ini"
cp $MERGED_CFG $APPWEB_CONFIG_PATH



delete_systemd_service() {
    local service="$1"
    echo -e "\nDeleting service: $service"
    # Use set +e to prevent script from exiting if a command fails
    set +e
    systemctl --user stop "$service"
    systemctl --user disable "$service"
    systemctl --user reset-failed "$service"
    rm -f ~/.config/systemd/user/"$service"
    set -e
}

# 0.1 delete service
if [ "$delete_service" = true ]; then
    if [ -z "$prog_type" ]; then
        echo -e "\n\nError: You must specify a program type using -t when using -delete"
        echo "Example: ./start.sh -t batch -n batch_name -delete"
        exit 1
    fi
    
    if [ "$prog_type" = "batch" ]; then
        if [ -z "$batch_name" ]; then
            echo -e "\n\nError: You must specify a batch name using -n when deleting a batch service"
            echo "Example: ./start.sh -t batch -n batch_name -delete"
            exit 1
        fi
        delete_systemd_service "batch@$batch_name.service"
    else
        delete_systemd_service "$prog_type.service"
    fi
    exit 0
fi

if [ "$delete_failed_services" = true ]; then
    # Get list of failed services using systemctl
    failed_services=$(systemctl --user list-units --type=service --state=failed --no-legend | awk '{print $2}')
    
    if [ -z "$failed_services" ]; then
        echo "No failed services found."
        exit 0
    fi
    
    echo "Found failed services:"
    echo "$failed_services"
    echo "Proceeding to delete them..."
    
    while IFS= read -r service; do
        delete_systemd_service "$service"
    done <<< "$failed_services"
    
    echo "All failed services have been deleted."
    exit 0
fi


# 1. Run the appweb server if no other arguments are provided (only -c)
# Example: ./start.sh [-c appweb.ini]
if [ -z "$other_args" ] && [ -z "$prog_type" ]; then
    # Find the process ID for the existing server process
    # ignore pgrep exit code(it will exit 1 if not found)
    set +e
    running_pid=`pgrep -u $USER -f "node.*coffee.*cmate.*app_web.ini"`
    set -e
    if [ -n "$running_pid" ]; then
      echo "found running, pid=$running_pid"
    fi
    if [ -z "$running_pid" ]; then
      # If the server is not running, start it
      echo "not found running, Start server process... "
      # Prepare envList with dynamically expanded variables
      envList="CMATE3_PATH=$CMATE3_PATH,CONFIG_PATH=$APPWEB_CONFIG_PATH,NODE_PATH=$NODE_PATH,COFFEE_PATH=$COFFEE_PATH,BUILT_DIR=$CFG_DIR/$BUILT_DIR"
      execStart="$NODE_PATH $COFFEE_PATH $CMATE3_PATH $APPWEB_CONFIG_PATH"
      # Construct the systemd service file
      coffee config.coffee -e $alert_email -s $SRC_DIR\
        -t appweb \
        -v "$envList" \
        -r "$execStart" \
        -w "$wanted_by" \
        -u "$USER" \
        -g "$GROUP" \
        -rs "$restart_service"

      echo $wanted_by
      echo $SYSTEMD_PATH
      # Only generate the systemd service file if wanted_by is set
      if [ -n "$wanted_by" ]; then
        cp $APPWEB_CONFIG_PATH $ETC_RMINI_DIR/app_web.ini
        exit 0
      fi

      echo "systemctl --user daemon-reload;systemctl --user start appweb.service"
      echo -e "\nYou can check the status of your service or stop it with the following commands:\nTo check the status:\n  systemctl --user status appweb.service\nTo stop the service:\n  systemctl --user stop appweb.service\n"
      cp $CFG_DIR/$BUILT_DIR/systemd/appweb.service $SYSTEMD_PATH
      systemctl --user daemon-reload;
      systemctl --user start appweb.service;
      pid=$(pgrep -u $USER -f "node.*coffee.*cmate.*app_web.ini")
      echo "pid=${pid}"
      cd $CFG_DIR
      mv $MERGED_CFG $BUILT_DIR/rmIni/config_${pid}.ini 
      echo "configuration saved to: $BUILT_DIR/rmIni/config_${pid}.ini" 
      echo "log file: $LOG_DIR/appweb_cmate.log"
      exit
    else
      # If the server is already running, reload it
      echo "Reload server ..."
      echo "log file: $LOG_DIR/appweb_cmate.log"
      systemctl --user daemon-reload;
      systemctl --user reload appweb.service;
      exit
    fi
fi

# 2.0 run batch only in command line
if [ -z "$prog_type" ] && [ -n "$other_args" ]; then
  echo "run batch only in command line"
  # if not has /built/rmIni/batch.ini, throw error
  if [ ! -f $BUILT_CONFIG_DIR/batch.ini ]; then
    coffee config.coffee
  fi
  export RMBASE_FILE_CFG=$BUILT_CONFIG_DIR/batch.ini
  export CGO_ENABLED=0
  echo "export RMBASE_FILE_CFG=$RMBASE_FILE_CFG"
  # check if the first argument ends with '.coffee', '.go', or '.bin'
  first_arg=$(echo "$other_args" | awk '{print $1}')
  echo "First argument: $first_arg"
  if [[ "$first_arg" == *.coffee ]]; then
      echo "The first argument is a CoffeeScript file."
      str="$NODE_PATH $COFFEE_PATH $SRC_DIR/$other_args > $LOG_DIR/${batch_name}.log  2>&1"
  elif [[ "$first_arg" == *.go ]]; then
      echo "The first argument is a go script."
      if [ -n "$src_go_dir" ]; then
        go_run_path="$SRC_GO_BASE_DIR/$src_go_dir"
        go_run_path=$(realpath "$go_run_path")
      else
        if [ -n "$SRC_GOAPP_DIR" ]; then
          go_run_path=$(realpath "$SRC_GOAPP_DIR")
        else
          go_run_path=""
        fi
      fi
      echo "go run path is: $go_run_path"
      # check if go path is empty
      if [ -z "$go_run_path" ]; then
        echo "You must specify the source directory for the Go application using -d option."
        exit 1
      fi
      # check go path
      GO_PATH=$(which go)
      if [ -z "$GO_PATH" ]; then
        echo "GO_PATH is not set, please set it in your environment."
        exit 1
      fi
      str="cd $go_run_path && $GO_PATH run $go_run_path/$other_args > $LOG_DIR/${batch_name}.log  2>&1"
  elif [[ "$first_arg" == *.bin ]]; then
      echo "The first argument is a compiled binary file."
      if [ -n "$src_go_dir" ]; then
        go_run_path="$SRC_GO_BASE_DIR/$src_go_dir"
        go_run_path=$(realpath "$go_run_path")
      else
        if [ -n "$SRC_GOAPP_DIR" ]; then
          go_run_path=$(realpath "$SRC_GOAPP_DIR")
        else
          go_run_path=""
        fi
      fi
      echo "binary run path is: $go_run_path"
      # check if go path is empty
      if [ -z "$go_run_path" ]; then
        echo "You must specify the source directory for the binary application using -d option."
        exit 1
      fi
      str="export CGO_ENABLED=0 && export RMBASE_FILE_CFG=$RMBASE_FILE_CFG && cd $go_run_path && $go_run_path/$other_args > $LOG_DIR/${batch_name}.log  2>&1"
  else
      echo "The first argument is not a CoffeeScript, Golang, or binary file, not support yet."
      exit 1
  fi
  
  echo -e "\n\nrunning command:\n------\n$str\n------"
  eval $str
  exit 0
fi

# 2. Handle program execution with parameters if they are provided
# Use the -t option to specify the type of program to run (batch|pyfpimg|unit_test|unit_testES|pyml)
# batch has to use -n option to specify the name of the batch
# If no custom config file is provided with -c, use the default config file for the specified type
# Example: ./start.sh -t batch lib/batchBase.coffee -cmd "batch/condos/fix_floorplanId.coffee dryrun" -cron '*-*-* 09:40:00'
case "$prog_type" in
  batch)
    # a. Verify the batch_name is provided
    if [ -z "$batch_name" ]; then
      echo "You must specify a batch name using -n batch_name"
      exit 1
    fi
    # If batch_name is not empty, continue with the rest of the script
    echo "Batch name: $batch_name"
    BATCH_CONFIG_PATH="$LOCAL_RMINI_DIR/batch.ini"

    # b. Create `batch.ini` and service file based on the provided arguments
    echo "Starting batch service..."
    if [ ${#config_files[@]} -eq 0 ]; then
      echo "use default built/batch.ini"
      cp $BUILT_CONFIG_DIR/batch.ini $BATCH_CONFIG_PATH
    else
      cp $MERGED_CFG $APPWEB_CONFIG_PATH
    fi
    envList="RMBASE_FILE_CFG=$BATCH_CONFIG_PATH"

    first_arg=$(echo "$other_args" | awk '{print $1}')
    # Output to verify
    echo "First argument: $first_arg"
    # Check if the first argument ends with '.coffee'
    if [[ "$first_arg" == *.coffee ]]; then
        echo "The first argument is a CoffeeScript file."
        str="$NODE_PATH $COFFEE_PATH $SRC_DIR/$other_args"
    elif [[ "$first_arg" == *.go ]]; then
        echo "The first argument is a go script."
        if [ -n "$src_go_dir" ]; then
          go_run_path="$SRC_GO_BASE_DIR/$src_go_dir"
          go_run_path=$(realpath "$go_run_path")
        else
          if [ -n "$SRC_GOAPP_DIR" ]; then
            go_run_path=$(realpath "$SRC_GOAPP_DIR")
          else
            go_run_path=""
          fi
        fi
        echo "go run path is: $go_run_path"
        # check if go path is empty
        if [ -z "$go_run_path" ]; then
          echo "You must specify the source directory for the Go application using -d option."
          exit 1
        fi
        GO_PATH=$(which go)
        str="/bin/bash -c 'export CGO_ENABLED=0 && export RMBASE_FILE_CFG=$BATCH_CONFIG_PATH && cd $go_run_path && $GO_PATH mod tidy && $GO_PATH run $go_run_path/$other_args'"
    elif [[ "$first_arg" == *.bin ]]; then
        echo "The first argument is a compiled binary file."
        if [ -n "$src_go_dir" ]; then
          go_run_path="$SRC_GO_BASE_DIR/$src_go_dir"
          go_run_path=$(realpath "$go_run_path")
        else
          if [ -n "$SRC_GOAPP_DIR" ]; then
            go_run_path=$(realpath "$SRC_GOAPP_DIR")
          else
            go_run_path=""
          fi
        fi
        echo "binary run path is: $go_run_path"
        # check if go path is empty
        if [ -z "$go_run_path" ]; then
          echo "You must specify the source directory for the binary application using -d option."
          exit 1
        fi
        str="/bin/bash -c 'export CGO_ENABLED=0 && export RMBASE_FILE_CFG=$BATCH_CONFIG_PATH && cd $go_run_path && $go_run_path/$other_args'"
    else
        echo "The first argument is not a CoffeeScript, Golang, or binary file."
        str=$SRC_DIR/$other_args
    fi

    execStart="$str"
    # Construct the systemd service file
    coffee config.coffee -e $alert_email -s $SRC_DIR\
      -t batch \
      -n "$batch_name" \
      -v "$envList" \
      -r "$execStart" \
      -w "$wanted_by" \
      -u "$USER" \
      -g "$GROUP" \
      -rs "$restart_service" \
      -cron "$cron_timer"
    # Only generate the systemd service file if wanted_by is set
    if [ -n "$wanted_by" ]; then
      cp $BATCH_CONFIG_PATH $ETC_RMINI_DIR/batch.ini
      exit 0
    fi

    # c. Reload the systemctl daemon and start the batch service
    echo "systemctl --user daemon-reload; systemctl --user start batch@$batch_name.service"
    echo -e "\nYou can check the status of your service or stop it with the following commands:\nTo check the status:\n  systemctl --user status batch@$batch_name.service\nTo stop the service:\n  systemctl --user stop batch@$batch_name.service"
    cp $CFG_DIR/$BUILT_DIR/systemd/batch@$batch_name.service $SYSTEMD_PATH
    # systemctl --user daemon-reload
    # systemctl --user start batch@$batch_name.service

    # d. when cron_timer is set, create a timer for the batch
    if [ -n "$cron_timer" ]; then
      echo -e "To check the status:\n  systemctl --user list-timers --all "
      cp $CFG_DIR/$BUILT_DIR/systemd/batch@$batch_name.timer $SYSTEMD_PATH
      systemctl --user enable batch@$batch_name.timer # timer start automatically on the next time
      systemctl --user start batch@$batch_name.timer # starts the timer now.
    else
      systemctl --user daemon-reload
      systemctl --user start batch@$batch_name.service
    fi
    echo -e "\nlog file: $LOG_DIR/batch_${batch_name}.log"
    ;;
  unit_test)
    ### ./start.sh -t unit_test [-cron '*-*-* 09:40:00']
    # a. Create `unit_test.ini` and service file based on the provided arguments
    echo "Running unit test service..."
    UT_CONFIG_PATH="$LOCAL_RMINI_DIR/unit_test.ini"
    if [ ${#config_files[@]} -eq 0 ]; then
      echo "use default built/unit_test.ini"
      cp $BUILT_CONFIG_DIR/unit_test.ini $UT_CONFIG_PATH
    else
      cp $MERGED_CFG $UT_CONFIG_PATH
    fi
    
    envList="COFFEE_PATH=$COFFEE_PATH,NODE_PATH=$NODE_PATH,CFG_PATH=$UT_CONFIG_PATH"
    # Conditional addition based on the content of alert_email
    if [ -n "$alert_email" ]; then
      execStart="$CFG_DIR/unitTest/testPassed.sh -d $CFG_DIR -e $alert_email"
    else
      execStart="$CFG_DIR/unitTest/testPassed.sh -d $CFG_DIR"
    fi 
    # Construct the systemd service file
    echo "running config.coffee with cmd_args=-e $alert_email -s $SRC_DIR -t unit_test -v $envList -r $execStart -w $wanted_by -u $USER -g $GROUP -cron $cron_timer -rs $restart_service"
    coffee config.coffee -e $alert_email -s $SRC_DIR\
      -t unit_test \
      -v "$envList" \
      -r "$execStart" \
      -w "$wanted_by" \
      -u "$USER" \
      -g "$GROUP" \
      -cron "$cron_timer" \
      -rs "$restart_service"
    # Only generate the systemd service file if wanted_by is set
    if [ -n "$wanted_by" ]; then
      cp $UT_CONFIG_PATH $ETC_RMINI_DIR/unit_test.ini
      exit 0
    fi

    # b. Reload the systemctl daemon and start the batch service
    echo "systemctl --user daemon-reload; systemctl --user start unit_test.service"
    echo -e "\nYou can check the status of your service or stop it with the following commands:\nTo check the status:\n  systemctl --user status unit_test.service\nTo stop the service:\n  systemctl --user stop unit_test.service"
    cp $CFG_DIR/$BUILT_DIR/systemd/unit_test.service $SYSTEMD_PATH
    # systemctl --user daemon-reload
    # systemctl --user start unit_test.service

    # c. when cron_timer is set, create a timer for the batch
    if [ -n "$cron_timer" ]; then
      echo -e "To check the status:\n  systemctl --user list-timers --all"
      cp $CFG_DIR/$BUILT_DIR/systemd/unit_test.timer $SYSTEMD_PATH
      systemctl --user enable unit_test.timer
      systemctl --user start unit_test.timer
    else
      systemctl --user daemon-reload
      systemctl --user start unit_test.service
    fi
    echo -e "\nlog file: $LOG_DIR/unit_test.log"
    ;;
  unit_testES)
    ### ./start.sh -t unit_testES [-cron '*-*-* 09:40:00']
    # a. Create `unit_testES.ini` and service file based on the provided arguments
    echo "Running unit test ES service..."
    UT_ES_CONFIG_PATH="$LOCAL_RMINI_DIR/unit_testES.ini"
    if [ ${#config_files[@]} -eq 0 ]; then
      echo "use default built/unit_testES.ini"
      cp $BUILT_CONFIG_DIR/unit_testES.ini $UT_ES_CONFIG_PATH
    fi
    envList="COFFEE_PATH=$COFFEE_PATH,NODE_PATH=$NODE_PATH,CFG_PATH=$UT_ES_CONFIG_PATH"
    # Conditional addition based on the content of alert_email
    if [ -n "$alert_email" ]; then
      execStart="$CFG_DIR/unitTest/testPassed.sh -d $CFG_DIR -e $alert_email"
    else
      execStart="$CFG_DIR/unitTest/testPassed.sh -d $CFG_DIR"
    fi 
    # Construct the systemd service file
    echo "running config.coffee with cmd_args=-e $alert_email -s $SRC_DIR -t unit_test -v $envList -r $execStart -w $wanted_by -u $USER -g $GROUP -cron $cron_timer -rs $restart_service"
    coffee config.coffee -e $alert_email -s $SRC_DIR\
      -t unit_testES \
      -v "$envList" \
      -r "$execStart" \
      -w "$wanted_by" \
      -u "$USER" \
      -g "$GROUP" \
      -cron "$cron_timer" \
      -rs "$restart_service"
    # Only generate the systemd service file if wanted_by is set
    if [ -n "$wanted_by" ]; then
      cp $UT_ES_CONFIG_PATH $ETC_RMINI_DIR/unit_testES.ini
      exit 0
    fi
    # b. Reload the systemctl daemon and start the batch service
    echo "systemctl --user daemon-reload; systemctl --user start unit_testES.service"
    echo -e "\nYou can check the status of your service or stop it with the following commands:\nTo check the status:\n  systemctl --user status unit_testES.service\nTo stop the service:\n  systemctl --user stop unit_testES.service"
    cp $CFG_DIR/$BUILT_DIR/systemd/unit_testES.service $SYSTEMD_PATH
    # systemctl --user daemon-reload
    # systemctl --user start unit_testES.service

    # c. when cron_timer is set, create a timer for the batch
    if [ -n "$cron_timer" ]; then
      echo -e "To check the status:\n  systemctl --user list-timers --all"
      cp $CFG_DIR/$BUILT_DIR/systemd/unit_testES.timer $SYSTEMD_PATH
      systemctl --user enable unit_testES.timer
      systemctl --user start unit_testES.timer
    else
      systemctl --user daemon-reload
      systemctl --user start unit_testES.service
    fi
    echo -e "log file: $LOG_DIR/unit_testES.log"
    ;;
  pyfpimg)
    ### ./start.sh -t pyfpimg --test
    # a. Create `pyfpimg.ini` and service file based on the provided arguments
    echo "Running pyfpimg service..."
    PYFPIMG_CONFIG_PATH="$LOCAL_RMINI_DIR/pyfpimg.ini"
    if [ ${#config_files[@]} -eq 0 ]; then
      echo "use default built/pyfpimg.ini"
      cp $BUILT_CONFIG_DIR/pyfpimg.ini $PYFPIMG_CONFIG_PATH
    else
      cp $MERGED_CFG $PYFPIMG_CONFIG_PATH
    fi
    cd $PYFPIMG_DIR
    POETRY_PATH=$(which poetry)
    PYTHON_PATH=$(poetry run which python)
    envList="RMBASE_FILE_PYTHON=$PYFPIMG_CONFIG_PATH"
    str=$other_args
    execStart="/bin/bash -c 'cd $PYFPIMG_DIR && $POETRY_PATH run $PYTHON_PATH $PYFPIMG_DIR/src/main.py $str '"
    # Construct the systemd service file
    cd $CFG_DIR
    coffee config.coffee -e $alert_email -s $SRC_DIR\
      -t pyfpimg \
      -v "$envList" \
      -r "$execStart" \
      -w "$wanted_by" \
      -u "$USER" \
      -g "$GROUP" \
      -rs "$restart_service" \
      -cron "$cron_timer"
    # Only generate the systemd service file if wanted_by is set
    if [ -n "$wanted_by" ]; then
      cp $PYFPIMG_CONFIG_PATH $ETC_RMINI_DIR/pyfpimg.ini
      exit 0
    fi
    # b. Reload the systemctl daemon and start the batch service
    echo "systemctl --user daemon-reload; systemctl --user start pyfpimg.service"
    echo -e "\nYou can check the status of your service or stop it with the following commands:\nTo check the status:\n  systemctl --user status pyfpimg.service\nTo stop the service:\n  systemctl --user stop pyfpimg.service\n"
    echo $PYFPIMG_CONFIG_PATH
    echo $CFG_DIR
    LOG_FILE_PYFPIMG=$(coffee $CFG_DIR/bin/getValueFromToml.coffee "$PYFPIMG_CONFIG_PATH" "pyfpimg.log.file")
    echo "log file: $LOG_FILE_PYFPIMG"
    cp $CFG_DIR/$BUILT_DIR/systemd/pyfpimg.service $SYSTEMD_PATH
    systemctl --user daemon-reload
    systemctl --user start pyfpimg.service
    ;;
  pyml)
    # TODO: Implement the pyml service
    ### a. Create `pyfpimg.ini` and service file based on the provided arguments
    # echo "Running pyml service..."
    #PYML_CONFIG_PATH="$LOCAL_RMINI_DIR/pyml.ini"
    echo "pyml service is not implemented yet!"
    exit 1
    # if [ ${#config_files[@]} -eq 0 ]; then
    #   echo "use default built/pyml.ini"
    #   cp $CFG_DIR/$BUILT_DIR/pyml.ini $PYML_CONFIG_PATH
    # else
    #   cp $MERGED_CFG $PYML_CONFIG_PATH
    # fi

    # # b. Reload the systemctl daemon and start the batch service
    # echo "systemctl --user daemon-reload; systemctl --user start pyml.service"
    # cp $CFG_DIR/$BUILT_DIR/systemd/pyml.service $SYSTEMD_PATH
    # systemctl --user daemon-reload
    # systemctl --user start pyml.service
    ;;
  *)
    echo "Unknown program type: $prog_type, use -t type"
    exit 1
    ;;
esac