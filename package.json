{"version": "0.0.1", "private": true, "description": "Configuration for rm-webapp using toml format", "scripts": {"start": "coffee config.coffee"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.realmaster.ca/"}, "homepage": "http://www.realmaster.ca/", "repository": {"type": "git", "url": "https://bitbucket.org/fredxiang/rmconfig.git"}, "engines": {"node": ">= 6", "npm": ">= 2"}, "dependencies": {"toml": "github:fxfred/toml#latest", "coffee": "^5.5.1", "fs": "^0.0.1-security", "path": "^0.12.7", "sharp": "^0.29.0"}, "main": "none.js", "devDependencies": {"coffeescript": "^2.7.0"}}