###
This script reads all the configuration files in the config directory, sorts them, applies environment overrides, and saves them to the built directory.
It also generates a systemd service file and an environment file for the service file.

Usage:
- Run the script without any arguments to process the configuration files.
  coffee config.coffee -e alertEmail -s srcPath -t type -n name -v envList -r execStart -w wantedBy

- options: all are optional
  -e: alert email
  -s: source path
  -t: type of systemd service file [appweb, batch, unit_test, unit_testES, pyfpimg, pyml]
  -n: name of batch systemd service file
  -v: environment variables list:'CMATE3_PATH=/home/<USER>/src/lib/cmate3.coffee','CONFIG_PATH=/home/<USER>/rmconfig/built/run/config.ini'
  -r: execStart command:'node coffee cmate3.cofee config.ini'
  -w: wantedBy: default.target or multi-user.target(startup) in service file [default.target,multi-user.target]. Default is default.target
  -cron: cron timer for batch service file
  -c: sort and save config file
  -cmd: command for batch service file
  -rs: restart_service, default is no, if set, the service will be restarted when it fails
  -delete: delete a specific systemd service, need to input -t and -n
  -delFailed: delete all failed systemd service

- results:
  - Configuration files are sorted and saved to the built directory.
  - A systemd service file is generated and saved to the built/systemd directory.
  - An environment file for the systemd service file is generated and saved to the built/systemd/env. It is for sending alert emails.

###
fs = require 'fs'
path = require 'path'
toml = require 'toml'
{ mergeConfigs, deepClone, convertListFormat } = require './bin/mergeConfigs'
{ extractComments, reinsertComments} = require './bin/commentHandler'
{ getImports, processImportFiles,handleImports } = require './bin/importHandler'

configPath = './'
#Initial default value, will be read from the configuration in main step0
srcPath =  '../appweb/src'
trebBaseDir = '/tmp'
creaBaseDir = '/tmp'
bcreBaseDir = '/tmp'
logPath = './logs'
sendEmailServiceName = 'send_email_on_failure'


# Recursively sort object keys
sortObject = (obj) ->
  if Array.isArray(obj)
    return obj  # Preserve arrays as is
  if typeof obj is 'object' and obj isnt null
    sortedObj = {}
    for key in Object.keys(obj).sort()
      sortedObj[key] = sortObject(obj[key])
    return sortedObj
  return obj


# Sort content without @import statements
sortContentWithoutImports = (contentWithoutImports) ->
  # Extract comments from content
  [contentWithoutComments, comments, outsideComments] = extractComments(contentWithoutImports)
  # Parse and sort the config
  parsedConfig = toml.parse(contentWithoutComments)
  sortedConfig = sortObject(parsedConfig)
  # Convert sorted object back to TOML string
  sortedContent = toml.stringify(sortedConfig)
  return reinsertComments(sortedContent, comments, outsideComments)


# Function to sort and save config folder files
sortAndSaveConfigFolderFiles = (configDir) ->
  configFiles = fs.readdirSync(configDir)
  for file in configFiles
    filePath = path.join(configDir, file)
    if fs.statSync(filePath).isFile() and file.endsWith('.ini')
      sortAndSaveConfigFile(filePath, filePath)

# Function to sort and save single config file
sortAndSaveConfigFile = (inputFile, outputFile) ->
  content = fs.readFileSync(inputFile, 'utf8')
  # Extract @import statements and content without @import
  [importStatements, contentWithoutImports] = getImports(content)
  # Sort content without @import statements
  sortedContentWithoutImports = sortContentWithoutImports(contentWithoutImports)
  # Add @import statements back
  if importStatements and importStatements.length > 0
    if sortedContentWithoutImports not in ['', '\n']
      output = importStatements.join('\n') + '\n\n' + sortedContentWithoutImports
    else
      output = importStatements.join('\n')
  else
    output = sortedContentWithoutImports
  # Write the sorted content back to file
  # console.log('Output file: '+outputFile)
  fs.writeFileSync(outputFile, output)


# build configs, apply env overrides, sort and save to built directory
buildAndSaveConfigs = (configs, builtDir, envFile, envTestFile, envTestESFile) ->
  for file, config of configs
    configOverrided = mergeEnvOverrides(file, config, envFile, envTestFile, envTestESFile)
    configOverridedAbsolutPath = convertToAbsolutePath(configOverrided)
    builtFilePath = path.join(builtDir, file)
    configOverridedAbsolutPathSorted = sortObject(configOverridedAbsolutPath)
    configOverridedAbsolutPathSortedConvertedList = convertListFormat(configOverridedAbsolutPathSorted)
    fs.writeFileSync(builtFilePath, toml.stringify(configOverridedAbsolutPathSortedConvertedList))


replaceEnvPath = (value)->
  value = value.replace(/\$\{configPath\}/g, configPath)
    .replace(/\$\{srcPath\}/g, srcPath)
    .replace(/\$\{trebBaseDir\}/g, trebBaseDir)
    .replace(/\$\{creaBaseDir\}/g, creaBaseDir)
    .replace(/\$\{bcreBaseDir\}/g, bcreBaseDir)
  return value


# recursive function to convert relative paths to absolute paths
convertToAbsolutePath = (config, basePath = process.cwd()) ->
  convertObject = (obj) ->
    for key, value of obj
      if typeof value is 'string'
        value = replaceEnvPath(value)
        if value.startsWith('./') or value.startsWith('../')
          obj[key] = path.resolve(basePath, value)
        else
          obj[key] = value
      else if Array.isArray(value)
        # Iterate through each element in the array and replace variables if it's a string
        obj[key] = value.map (item) ->
          if typeof item is 'string'
            item = replaceEnvPath(item)
            if item.startsWith('./') or item.startsWith('../')
              path.resolve(basePath, item)
            else
              item
          else
            item
      else if typeof value is 'object' and not Array.isArray(value)
        convertObject(value)
    obj

  # Create a deep copy of the config to avoid modifying the original
  configCopy = deepClone(config)
  convertObject(configCopy)


#.env.toml overrides config
mergeEnvOverrides = (file, config, envFile, envTestFile, envTestESFile) ->
  mergeEnvConfig = config

  # Check if the filename ends with _test.ini
  if /_test\.ini$/.test(file)
    # If _test.ini file exists, merge its configuration
    if fs.existsSync(envTestFile)
      envTestConfig = toml.parse(fs.readFileSync(envTestFile))
      mergeEnvConfig = mergeConfigs(config, envTestConfig, true)
  else if /_testES\.ini$/.test(file)
    # If _test.ini file exists, merge its configuration
    if fs.existsSync(envTestESFile)
      envTestESConfig = toml.parse(fs.readFileSync(envTestESFile))
      mergeEnvConfig = mergeConfigs(config, envTestESConfig, true)
  else
    # If not a _test.ini file, merge the standard envFile config
    if fs.existsSync(envFile)
      envConfig = toml.parse(fs.readFileSync(envFile))
      mergeEnvConfig = mergeConfigs(config, envConfig, true)
  
  return mergeEnvConfig


# Function to replace ${ENV_PATH} in service files and write to systemdBuiltDir
replaceEnvPathInServiceFiles = (dir, envPath, outputDir) ->
  absoluteEnvPath = path.resolve(envPath)
  absoluteCFGPath = path.resolve(configPath)
  absoluteLOGPath = path.resolve(logPath)
  serviceFiles = fs.readdirSync(dir)
  for file in serviceFiles
    serviceFilePath = path.join(dir, file)
    outputFilePath = path.join(outputDir, file)
    if path.extname(file) is '.service' or path.extname(file) is '.timer'
      # Check if outputFilePath exists, and delete it if it does
      if fs.existsSync(outputFilePath)
        fs.unlinkSync(outputFilePath)
      data = fs.readFileSync(serviceFilePath, 'utf8')
      # console.log "Processing0 #{data}"
      if data.includes('${ENV_PATH}') or data.includes('${CFG_PATH}') or data.includes('${LOG_PATH}')
        updatedData = data.replace /\$\{ENV_PATH\}/g, absoluteEnvPath
        updatedData = updatedData.replace /\$\{CFG_PATH\}/g, absoluteCFGPath
        updatedData = updatedData.replace /\$\{LOG_PATH\}/g, absoluteLOGPath
        fs.writeFileSync(outputFilePath, updatedData)
      else
        fs.writeFileSync(outputFilePath, data)


generateSendEmailSystemdFile = (systemdTplDir,systemdBuiltDir,etcSystemdBuiltDir,alertEmail) ->
  envVariables = 
    SEND_EMAIL_PATH: path.resolve(configPath, 'bin/sendEmail.sh')
    ALERT_EMAIL: alertEmail
  envString = Object.entries(envVariables).map(([key, value]) -> "Environment=\"#{key}=#{value}\"").join('\n')
  serviceFilePath = path.resolve(systemdTplDir, "#{sendEmailServiceName}@.service")
  systemdFilePath = path.resolve(systemdBuiltDir, "#{sendEmailServiceName}@.service")
  etcSystemdFilePath = path.resolve(etcSystemdBuiltDir, "#{sendEmailServiceName}@.service")
  data = fs.readFileSync(serviceFilePath, 'utf8')
  data = data.replace(/\$\{ENV\}/g, envString)
  fs.writeFileSync(systemdFilePath, data)
  console.log "Generated systemd service file: #{systemdFilePath}"
  fs.writeFileSync(etcSystemdFilePath, data)
  console.log "Generated systemd service file: #{etcSystemdFilePath}"
  console.log "\nIf you need run in root, please do following:\nsudo cp #{etcSystemdFilePath} /etc/systemd/system/\n"


getDefaultFailureSendEmail = (appwebFile)->
  appwebConfig = toml.parse(fs.readFileSync(appwebFile))
  alertEmail = appwebConfig.contact.alertEmail
  return alertEmail

getDefaultSrcPath = (envFile)->
  appwebConfig = toml.parse(fs.readFileSync(envFile))
  srcPath = appwebConfig.serverBase.srcPath
  return srcPath

getDefaultBaseAndLogPath = (envFile)->
  console.log "envFile: #{envFile}"
  appwebConfig = toml.parse(fs.readFileSync(envFile))
  trebBaseDir = appwebConfig.serverBase?.trebBaseDir ? trebBaseDir
  creaBaseDir = appwebConfig.serverBase?.creaBaseDir ? creaBaseDir
  bcreBaseDir = appwebConfig.serverBase?.bcreBaseDir ? bcreBaseDir
  logPath = appwebConfig.log?.path ? logPath
  return [trebBaseDir,creaBaseDir,bcreBaseDir,logPath]


# Helper function to write the systemd service file
writeSystemdServiceFile = (envList, execStart, tValue, nValue, user, group, wantedBy, cronTimer, systemdTplDir,systemdBuiltDir,etcSystemdBuiltDir,restart_service) ->
  serviceName= tValue
  if wantedBy is 'multi-user.target'
    outputPath = etcSystemdBuiltDir
  else
    outputPath = systemdBuiltDir
  
  if tValue is 'batch'
    serviceFilePath = path.resolve(systemdTplDir, "#{tValue}@.service")
    outputFilePath = path.resolve(outputPath, "#{tValue}@#{nValue}.service")
    outputTimerPath = path.resolve(outputPath, "#{tValue}@#{nValue}.timer")
    serviceName = "#{tValue}@#{nValue}"
  else
    serviceFilePath = path.resolve(systemdTplDir, "#{tValue}.service")
    outputFilePath = path.resolve(outputPath, "#{tValue}.service")
    outputTimerPath = path.resolve(outputPath, "#{tValue}.timer")
  absoluteLOGPath = path.resolve(logPath)
  data = fs.readFileSync(serviceFilePath, 'utf8')
  if wantedBy is 'multi-user.target'
    user_group = 'User=' + user + '\nGroup=' + group
  else
    wantedBy = 'default.target'
    user_group = ""
  if restart_service is 'true'
    restart_service_str = 'on-failure\nRestartSec=5s'
  else
    restart_service_str = 'no'
  # console.log "envList1",envList
  envString = envList.map( (env) -> "Environment=\"#{env}\"").join("\n")
  data = data.replace(/\$\{ENV\}/g, envString)
  data = data.replace(/\$\{EXEC_START\}/g, execStart)
  data = data.replace(/\$\{LOG_PATH\}/g, absoluteLOGPath)
  data = data.replace(/\$\{WANTED_BY\}/g, wantedBy)
  data = data.replace(/\$\{USER_GROUP\}/g, user_group)
  data = data.replace(/\$\{RESTART_SERVICE\}/g, restart_service_str)

  fs.writeFileSync(outputFilePath, data)
  if cronTimer
    timerData = fs.readFileSync(path.resolve(systemdTplDir, "template.timer"), 'utf8')
    name = nValue || tValue
    timerData = timerData.replace(/\$\{BATCH_NAME\}/g, name)
    timerData = timerData.replace(/\$\{CRON_TIMER\}/g, cronTimer)
    fs.writeFileSync(outputTimerPath, timerData)
  if wantedBy is 'multi-user.target'
    console.log "Generated systemd service file: #{outputFilePath}"
    console.log "\nPlease use sudo run:\nsudo cp #{outputFilePath} /etc/systemd/system/"
    if cronTimer
      console.log "sudo cp #{outputTimerPath} /etc/systemd/system/"
      console.log "sudo systemctl enable #{serviceName}.timer\n"
      console.log "To check the status:\n  systemctl list-timers\n"
    else
      console.log "sudo systemctl enable #{serviceName}\nsudo systemctl start #{serviceName}\n"
  else
    console.log "Generated systemd service file: #{outputFilePath}"
    if cronTimer
      console.log "Generated systemd timer file: #{outputTimerPath}"
  return outputFilePath


getExecutablePath = (command) ->
  try
    path = execSync("which #{command}").toString().trim()
    console.log "#{command} path: #{path}"
    path
  catch error
    console.error "Error finding path for #{command}: #{error.message}"
    null


getCommandLineArgs = ->
  args = process.argv.slice(2)
  console.log("running config with cmd_args \t=#{args}")
  tValue = null
  nValue = null
  otherArgs = []
  validTValues = ['appweb', 'batch', 'unit_test', 'unit_testES', 'pyfpimg', 'pyml']
  for i in [0...args.length]
    arg = args[i]
    if arg == '-e'
      alertEmail = args[i + 1] if args[i + 1]
    else if arg == '-s' #srcPath
      srcPath = args[i + 1] if args[i + 1]
    else if arg == '-t' #tValue
      tValue = args[i + 1] if args[i + 1]
      if tValue and not tValue in validTValues
        console.error "Invalid value for -t. Accepted values are: #{validTValues.join(', ')}"
        process.exit(1)
    else if arg == '-n' and tValue == 'batch' #nValue
      nValue = args[i + 1] if args[i + 1]
    else if arg == '-v' #envList
      envList = args[i + 1].split(',')
    else if arg == '-r' #execStart
      execStart = args[i + 1]
    else if arg == '-u' #user
      user = args[i + 1]
    else if arg == '-g' #group
      group = args[i + 1]
    else if arg == '-w' #wantedBy
      wantedBy = args[i + 1]
    else if arg == '-cron' #cron
      cronTimer = args[i + 1]
    else if arg == '-c' #sortConfig
      sortConfig = args[i + 1]
    else if arg == '-rs' #restart_service
      restart_service = args[i + 1]
    else if arg.startsWith('-')
      # ignore other flags
      continue
    else
      otherArgs.push arg
  if tValue == 'batch' and not nValue?
    throw new Error("When '-t batch' is chosen, '-n name' must be set.")
  # console.log "alertEmail: #{alertEmail}"
  # console.log "srcPath: #{srcPath}"
  # console.log "tValue: #{tValue}"
  # console.log "nValue: #{nValue}"
  # console.log "envList: #{envList}"
  # console.log "execStart: #{execStart}"
  # console.log "user: #{user}"
  # console.log "group: #{group}"
  # console.log "wantedBy: #{wantedBy}"
  # console.log "sortConfig: #{sortConfig}"
  # console.log "restart_service: #{restart_service}"
  return [alertEmail, srcPath, envList, execStart, tValue, nValue, user, group,wantedBy,cronTimer,sortConfig,restart_service]


main = ->
  try
    configDir = path.resolve(__dirname, 'config')
    builtDir = path.resolve(__dirname, 'built/config')
    rmIniDir = path.resolve(__dirname, 'built/rmIni')
    envFile = path.resolve(__dirname, 'local.ini')
    envTestFile = path.resolve(__dirname, 'local.test.ini')
    envTestESFile = path.resolve(__dirname, 'local.testES.ini')
    systemdTplDir = path.resolve(__dirname, 'systemd')
    systemdBuiltDir = path.resolve(__dirname, 'built/systemd')
    etcSystemdBuiltDir = path.resolve(__dirname, 'built/etc/systemd')
    systemdEnvFile = path.resolve(__dirname, 'built/systemd/env')
    appwebFile = path.resolve(__dirname,'built/app_web.ini')

    if not fs.existsSync(rmIniDir)
      fs.mkdirSync(rmIniDir, { recursive: true })
    if not fs.existsSync(systemdBuiltDir)
      fs.mkdirSync(systemdBuiltDir, { recursive: true })
    if not fs.existsSync(etcSystemdBuiltDir)
      fs.mkdirSync(etcSystemdBuiltDir, { recursive: true })

    [alertEmail, srcPath, envList, execStart, tValue, nValue, user, group, wantedBy, cronTimer, sortConfig, restart_service] = getCommandLineArgs() 
    # Step 0: Get alertEmail and srcPath
    if not srcPath
      if fs.existsSync(envFile)
        srcPath=getDefaultSrcPath(envFile)
      else if fs.existsSync(envTestFile)
        srcPath=getDefaultSrcPath(envTestFile)
      else if fs.existsSync(envTestESFile)
        srcPath=getDefaultSrcPath(envTestESFile)
    if not alertEmail and fs.existsSync(appwebFile)
      alertEmail=getDefaultFailureSendEmail(appwebFile)
    if not alertEmail
      alertEmail='<EMAIL>,<EMAIL>'
    # get trebBaseDir,creaBaseDir,bcreBaseDir logPath from envFile
    [trebBaseDir,creaBaseDir,bcreBaseDir,logPath] = getDefaultBaseAndLogPath(envFile)

    # Sort and save -c input config file
    if sortConfig
      sortedConfig = sortConfig + '.sorted'
      sortAndSaveConfigFile(sortConfig,sortedConfig)
      process.exit(0)

    # Genereate env file for systemd service and replace ${ENV_PATH} in service files
    if tValue
      systemdFile=writeSystemdServiceFile(envList, execStart, tValue, nValue, user, group, wantedBy,cronTimer,systemdTplDir,systemdBuiltDir,etcSystemdBuiltDir,restart_service)
      process.exit(0)

    # Step 1: Sort and save all files while preserving @import statements
    sortAndSaveConfigFolderFiles(configDir)

    # Step 2: Process all import files
    processedConfigs = processImportFiles(configDir)

    # Step 3: Build and save all configs
    buildAndSaveConfigs(processedConfigs, builtDir, envFile, envTestFile, envTestESFile)
    console.log('Configuration files sorted and saved successfully.')

    # Step 4: Generate send_email_on_failure service file and write to systemdBuiltDir and etcSystemdBuiltDir
    generateSendEmailSystemdFile(systemdTplDir,systemdBuiltDir,etcSystemdBuiltDir,alertEmail)

    process.exit(0)
  catch error
    console.error('Error processing configuration files:', error)
    process.exit(1)

main()
