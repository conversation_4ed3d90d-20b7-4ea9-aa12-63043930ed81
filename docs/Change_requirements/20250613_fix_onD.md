# 需求 [fix_onD]

## 反馈

1. 房源merge时对`onD`限制的配置不起作用
2. 需要增加参数,放宽房源merge时的`onD`限制

## 需求提出人:   Fred, Rain

## 修改人：      <PERSON><PERSON>

## 提出日期:     2025-06-12

## 原因

1. 文件对`onDMergeDateSpanForDiffSid`,`onDMergeDateSpanForSameSid`,`onDMergeDateSpanForDiffSidAndNoPho`的参数位置设置错误,应该设置在`serverBase`下。

## 解决办法

1. 修改参数的位置
2. 添加`onDRangeExpansionValue`参数到`serverBase`下,以用于放宽房源merge时的`onD`限制

## 参数含义 - 可根据实际数据进行调整

1. onDMergeDateSpanForDiffSid: 当房源`sid`不一致时,`onD`的限制天数,默认 1 天
2. onDMergeDateSpanForSameSid: 当房源`sid`一致时,`onD`的限制天数,默认 15 天
3. onDMergeDateSpanForDiffSidAndNoPho: 当房源`sid`不一致且没有'`pho`时,`onD`的限制天数,默认 7 天
4. onDRangeExpansionValue: 当房源`sid`不一致时,使用`onDMergeDateSpanForDiffSid`未找到merge房源时,放宽`onD`的限制天数,默认 5 天

## 确认日期:    2025-06

## online-step

1. 拉取最新代码,编译
